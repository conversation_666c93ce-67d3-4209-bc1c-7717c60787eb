const { chromium } = require('playwright');
const fs = require('fs');

async function quickTest() {
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    const allJobs = [];
    const maxPages = 2; // Only scrape first 2 pages for quick testing
    
    try {
        console.log('Navigating to bursaries page...');
        await page.goto('https://www.spane4all.co.za/bursaries', { waitUntil: 'domcontentloaded', timeout: 60000 });
        console.log('Page loaded, waiting for content...');
        
        await page.waitForSelector('.card-wrapper', { timeout: 30000 });
        console.log('Card wrapper found!');
        
        for (let currentPage = 1; currentPage <= maxPages; currentPage++) {
            console.log(`Scraping page ${currentPage}...`);
            
            const applyButtons = await page.locator('a.btn.btn-primary.mt-2:has-text("Apply")').all();
            console.log(`Found ${applyButtons.length} Apply buttons on page ${currentPage}`);
            
            // Process only first 3 jobs per page for quick testing
            const maxJobsPerPage = Math.min(3, applyButtons.length);
            
            for (let i = 0; i < maxJobsPerPage; i++) {
                try {
                    const href = await applyButtons[i].getAttribute('href');
                    console.log(`Processing job ${i + 1}: ${href}`);
                    
                    await applyButtons[i].click();
                    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                    
                    await page.waitForSelector('.card-body', { timeout: 10000 });
                    
                    const jobData = await page.evaluate(() => {
                        const data = {};
                        
                        const titleElement = document.querySelector('h1');
                        data.title = titleElement ? titleElement.textContent.trim() : '';
                        
                        const cardBody = document.querySelector('.card-body');
                        if (cardBody) {
                            const orgElement = cardBody.querySelector('p.card-text strong');
                            data.organization = orgElement ? orgElement.textContent.trim() : '';
                            
                            const paragraphs = cardBody.querySelectorAll('p');
                            paragraphs.forEach(p => {
                                const text = p.textContent.trim();
                                if (text.includes('Employment Type:')) {
                                    data.employmentType = text.replace('Employment Type:', '').trim();
                                } else if (text.includes('Job Location:')) {
                                    data.jobLocation = text.replace('Job Location:', '').trim();
                                } else if (text.includes('Base Salary:')) {
                                    data.baseSalary = text.replace('Base Salary:', '').trim();
                                } else if (text.includes('Closing Date:')) {
                                    const closingDateSpan = p.querySelector('#formatted-date');
                                    if (closingDateSpan) {
                                        data.closingDate = closingDateSpan.textContent.trim();
                                    } else {
                                        data.closingDate = text.replace('Closing Date:', '').trim();
                                    }
                                }
                            });
                            
                            const imgElement = cardBody.querySelector('img.card-img-top');
                            data.image = imgElement ? imgElement.getAttribute('src') : '';
                            
                            const applyButton = cardBody.querySelector('a.btn.btn-primary');
                            data.applyUrl = applyButton ? applyButton.getAttribute('href') : '';
                        }
                        
                        data.sourceUrl = window.location.href;
                        
                        return data;
                    });
                    
                    if (jobData) {
                        allJobs.push(jobData);
                        console.log(`Scraped job: ${jobData.organization || 'Unknown'}`);
                    }
                    
                    await page.goBack();
                    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                    await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                    
                } catch (error) {
                    console.error(`Error processing job ${i + 1}:`, error.message);
                }
            }
            
            // Move to next page if not the last page
            if (currentPage < maxPages) {
                const nextButton = await page.locator('a:has-text("Next")').first();
                const isNextButtonVisible = await nextButton.isVisible().catch(() => false);
                
                if (isNextButtonVisible) {
                    console.log(`Moving to page ${currentPage + 1}...`);
                    await nextButton.click();
                    await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                    await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                } else {
                    console.log('No more pages found.');
                    break;
                }
            }
        }
        
    } catch (error) {
        console.error('Error during scraping:', error);
    } finally {
        await browser.close();
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `quick_test_${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(allJobs, null, 2));
    console.log(`\nQuick test completed! Found ${allJobs.length} jobs.`);
    console.log(`Results saved to: ${filename}`);
    
    // Display results
    if (allJobs.length > 0) {
        console.log('\nSample results:');
        allJobs.forEach((job, index) => {
            console.log(`\n--- Job ${index + 1} ---`);
            console.log(`Title: ${job.title || 'N/A'}`);
            console.log(`Organization: ${job.organization || 'N/A'}`);
            console.log(`Employment Type: ${job.employmentType || 'N/A'}`);
            console.log(`Location: ${job.jobLocation || 'N/A'}`);
            console.log(`Salary: ${job.baseSalary || 'N/A'}`);
            console.log(`Closing Date: ${job.closingDate || 'N/A'}`);
            console.log(`Apply URL: ${job.applyUrl || 'N/A'}`);
        });
    }
    
    return allJobs;
}

quickTest().catch(console.error);
