const { scrapeJobs, CATEGORIES } = require('./bursary-scraper');
const fs = require('fs');

async function scrapeAllCategories(maxPagesPerCategory = 10) {
    console.log('🚀 Starting comprehensive scraping of all categories');
    console.log(`📄 Limiting to first ${maxPagesPerCategory} pages per category`);
    console.log('=' .repeat(60));
    
    const results = {};
    const startTime = Date.now();
    
    for (const [category, url] of Object.entries(CATEGORIES)) {
        console.log(`\n🔄 Starting ${category.toUpperCase()}`);
        console.log(`📍 URL: ${url}`);
        
        try {
            const result = await scrapeJobs(category, maxPagesPerCategory);
            results[category] = {
                success: true,
                ...result.metrics,
                jobs: result.jobs
            };
            
            console.log(`✅ ${category.toUpperCase()} completed successfully!`);
            console.log(`   📊 Jobs found: ${result.metrics.totalJobs}`);
            console.log(`   📄 Pages scraped: ${result.metrics.totalPagesScraped}`);
            console.log(`   ⏱️  Time taken: ${result.metrics.totalTimeSeconds.toFixed(2)}s`);
            
        } catch (error) {
            console.error(`❌ ${category.toUpperCase()} failed:`, error.message);
            results[category] = {
                success: false,
                error: error.message,
                totalJobs: 0,
                totalPagesScraped: 0
            };
        }
        
        // Wait between categories to be respectful
        console.log('⏳ Waiting 5 seconds before next category...');
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    // Generate comprehensive summary
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE SCRAPING SUMMARY');
    console.log('='.repeat(60));
    
    let totalJobs = 0;
    let totalPages = 0;
    let successfulCategories = 0;
    
    Object.entries(results).forEach(([category, result]) => {
        const status = result.success ? '✅' : '❌';
        console.log(`${status} ${category.toUpperCase().padEnd(15)} | ${result.totalJobs.toString().padStart(4)} jobs | ${result.totalPagesScraped.toString().padStart(3)} pages`);
        
        if (result.success) {
            totalJobs += result.totalJobs;
            totalPages += result.totalPagesScraped;
            successfulCategories++;
        }
    });
    
    console.log('-'.repeat(60));
    console.log(`📈 TOTALS: ${totalJobs} jobs from ${totalPages} pages`);
    console.log(`✅ Successful categories: ${successfulCategories}/${Object.keys(CATEGORIES).length}`);
    console.log(`⏱️  Total time: ${(totalTime / 60).toFixed(1)} minutes`);
    
    // Save comprehensive results
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const summaryFile = `all_categories_summary_${timestamp}.json`;
    
    const summary = {
        timestamp: new Date().toISOString(),
        maxPagesPerCategory,
        totalTime: totalTime,
        totalJobs,
        totalPages,
        successfulCategories,
        categories: results
    };
    
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log(`💾 Summary saved to: ${summaryFile}`);
    
    // Save all jobs in one file
    const allJobs = [];
    Object.entries(results).forEach(([category, result]) => {
        if (result.success && result.jobs) {
            result.jobs.forEach(job => {
                allJobs.push({
                    ...job,
                    category: category
                });
            });
        }
    });
    
    if (allJobs.length > 0) {
        const allJobsFile = `all_jobs_${timestamp}.json`;
        fs.writeFileSync(allJobsFile, JSON.stringify(allJobs, null, 2));
        console.log(`💾 All jobs saved to: ${allJobsFile}`);
    }
    
    console.log('\n🎉 Comprehensive scraping completed!');
    return summary;
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    const maxPages = args[0] ? parseInt(args[0]) : 10;
    
    if (isNaN(maxPages) || maxPages <= 0) {
        console.error('❌ Invalid page limit. Please provide a positive number.');
        console.log('Usage: node scrape-all-categories.js [maxPages]');
        console.log('Example: node scrape-all-categories.js 10');
        process.exit(1);
    }
    
    console.log(`🎯 Scraping first ${maxPages} pages of each category`);
    scrapeAllCategories(maxPages).catch(console.error);
}

module.exports = { scrapeAllCategories };
