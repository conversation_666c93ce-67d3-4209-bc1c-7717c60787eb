const { chromium } = require('playwright');
const fs = require('fs');

// Available categories to scrape
const CATEGORIES = {
    bursaries: 'https://www.spane4all.co.za/bursaries',
    internships: 'https://spane4all.co.za/internships',
    jobs: 'https://spane4all.co.za/jobs',
    scholarships: 'https://www.spane4all.co.za/scholarships',
    general_jobs: 'https://www.spane4all.co.za/general_jobs',
    government_intern: 'https://www.spane4all.co.za/government_intern',
    government_job: 'https://www.spane4all.co.za/government_job'
};

async function scrapeJobs(category = 'bursaries', maxPages = null) {
    // Validate category
    if (!CATEGORIES[category]) {
        throw new Error(`Invalid category: ${category}. Available categories: ${Object.keys(CATEGORIES).join(', ')}`);
    }

    const browser = await chromium.launch({ headless: false }); // Set to true for headless mode
    const page = await browser.newPage();

    const allJobs = [];
    let currentPage = 1;
    const startTime = Date.now();
    let totalPagesScraped = 0;
    let successfulJobs = 0;
    let failedJobs = 0;
    
    try {
        // Navigate to the first page
        const baseUrl = CATEGORIES[category];
        console.log(`\n=== SCRAPING ${category.toUpperCase()} ===`);
        console.log(`Navigating to ${baseUrl}...`);
        await page.goto(baseUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
        console.log('Page loaded, waiting for content...');

        // Wait for the card wrapper to be visible and content to load
        await page.waitForSelector('.card-wrapper', { timeout: 30000 });
        console.log('Card wrapper found!');

        // Wait a bit more for JavaScript to load all cards
        await page.waitForTimeout(3000);

        // Check total pages available
        const totalPagesInfo = await page.evaluate(() => {
            // Look for pagination info like "Page 1 of 222"
            const pageInfo = document.body.textContent.match(/Page\s+\d+\s+of\s+(\d+)/i);
            return pageInfo ? parseInt(pageInfo[1]) : null;
        });

        if (totalPagesInfo) {
            console.log(`📄 Total pages available: ${totalPagesInfo}`);
            if (maxPages === null) {
                console.log(`⚠️  Warning: This will scrape all ${totalPagesInfo} pages. Consider setting a limit!`);
            } else {
                console.log(`📊 Will scrape ${Math.min(maxPages, totalPagesInfo)} pages`);
            }
        }
        
        while (true) {
            console.log(`\n--- Scraping page ${currentPage} ---`);
            totalPagesScraped = currentPage;

            // Wait for content to load completely
            await page.waitForTimeout(2000);

            // Find all job links on the current page
            let jobLinks = [];

            try {
                // Get all job links with their hrefs
                jobLinks = await page.evaluate(() => {
                    // Try multiple selectors to find job links
                    let buttons = Array.from(document.querySelectorAll('a.btn.btn-primary.mt-2'));
                    if (buttons.length === 0) {
                        buttons = Array.from(document.querySelectorAll('a.btn.btn-primary'));
                    }
                    if (buttons.length === 0) {
                        buttons = Array.from(document.querySelectorAll('a[href*="/job/"]'));
                    }

                    return buttons
                        .filter(btn => btn.href && btn.href.includes('/job/'))
                        .map(btn => btn.href);
                });

                console.log(`Found ${jobLinks.length} job links on page ${currentPage}`);

                if (jobLinks.length === 0) {
                    console.log('⚠️  No job links found, waiting for content to load...');
                    await page.waitForTimeout(5000);

                    // Retry finding job links
                    jobLinks = await page.evaluate(() => {
                        let buttons = Array.from(document.querySelectorAll('a.btn.btn-primary.mt-2, a.btn.btn-primary, a[href*="/job/"]'));
                        return buttons
                            .filter(btn => btn.href && btn.href.includes('/job/'))
                            .map(btn => btn.href);
                    });

                    console.log(`Found ${jobLinks.length} job links after retry`);
                    if (jobLinks.length === 0) {
                        console.log('❌ No jobs found on this page, skipping...');
                        break;
                    }
                }
            } catch (error) {
                console.error(`Error finding job links on page ${currentPage}:`, error.message);
                break;
            }
            
            // Process each job by navigating directly to its URL
            for (let i = 0; i < jobLinks.length; i++) {
                try {
                    const jobUrl = jobLinks[i];
                    console.log(`Processing job ${i + 1}: ${jobUrl}`);

                    // Navigate directly to the job page
                    await page.goto(jobUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
                    
                    // Wait for page to fully load with multiple strategies
                    let cardBodyFound = false;

                    // Strategy 1: Wait for card-body
                    try {
                        await page.waitForSelector('.card-body', { timeout: 15000 });
                        cardBodyFound = true;
                    } catch (e) {
                        // Strategy 2: Wait for any content indicators
                        try {
                            await page.waitForSelector('h1, .card, .container', { timeout: 10000 });
                            // Check if card-body exists now
                            const cardBody = await page.$('.card-body');
                            cardBodyFound = !!cardBody;
                        } catch (e2) {
                            console.log(`⚠️  Page structure different for job ${i + 1}, trying to extract anyway...`);
                        }
                    }

                    // Extract job information from the page
                    const jobData = await page.evaluate(() => {
                        const data = {};

                        // Extract job title from h1
                        const titleElement = document.querySelector('h1');
                        data.title = titleElement ? titleElement.textContent.trim() : '';

                        // Extract information from the card body
                        const cardBody = document.querySelector('.card-body');
                        if (cardBody) {
                            // Extract organization name
                            const orgElement = cardBody.querySelector('p.card-text strong');
                            data.organization = orgElement ? orgElement.textContent.trim() : '';

                            // Extract employment type, location, salary, closing date
                            const paragraphs = cardBody.querySelectorAll('p');
                            paragraphs.forEach(p => {
                                const text = p.textContent.trim();
                                if (text.includes('Employment Type:')) {
                                    data.employmentType = text.replace('Employment Type:', '').trim();
                                } else if (text.includes('Job Location:')) {
                                    data.jobLocation = text.replace('Job Location:', '').trim();
                                } else if (text.includes('Base Salary:')) {
                                    data.baseSalary = text.replace('Base Salary:', '').trim();
                                } else if (text.includes('Closing Date:')) {
                                    // Handle the closing date which might have a span element
                                    const closingDateSpan = p.querySelector('#formatted-date');
                                    if (closingDateSpan) {
                                        data.closingDate = closingDateSpan.textContent.trim();
                                    } else {
                                        data.closingDate = text.replace('Closing Date:', '').trim();
                                    }
                                }
                            });

                            // Extract image and make it absolute URL
                            const imgElement = cardBody.querySelector('img.card-img-top');
                            if (imgElement) {
                                const imgSrc = imgElement.getAttribute('src');
                                if (imgSrc) {
                                    // Convert relative URLs to absolute URLs
                                    data.image = imgSrc.startsWith('http') ? imgSrc : `https://spane4all.co.za${imgSrc}`;
                                } else {
                                    data.image = '';
                                }
                            } else {
                                data.image = '';
                            }

                            // Extract Apply Now button URL
                            const applyButton = cardBody.querySelector('a.btn.btn-primary');
                            data.applyUrl = applyButton ? applyButton.getAttribute('href') : '';
                        }

                        // Extract description
                        const descriptionHeading = Array.from(document.querySelectorAll('h2')).find(el =>
                            el.textContent.trim() === 'Description'
                        );
                        if (descriptionHeading && descriptionHeading.nextElementSibling) {
                            data.description = descriptionHeading.nextElementSibling.textContent.trim();
                        }

                        return data;
                    });
                    
                    if (jobData) {
                        allJobs.push(jobData);
                        successfulJobs++;
                        console.log(`✓ Scraped job: ${jobData.organization || jobData.title || 'Unknown'}`);
                    } else {
                        failedJobs++;
                        console.log(`✗ Failed to extract job data`);
                    }
                    
                } catch (error) {
                    failedJobs++;
                    console.error(`✗ Error processing job ${i + 1}:`, error.message);
                }
            }

            // Navigate back to the main page to go to next page
            console.log(`Returning to main page for pagination...`);
            const currentPageUrl = `${baseUrl}?page=${currentPage}`;
            await page.goto(currentPageUrl, { waitUntil: 'domcontentloaded', timeout: 30000 });
            await page.waitForSelector('.card-wrapper', { timeout: 15000 });
            
            // Look for Next button to go to next page
            const nextButton = await page.locator('a:has-text("Next")').first();
            const isNextButtonVisible = await nextButton.isVisible().catch(() => false);
            
            if (isNextButtonVisible && (maxPages === null || currentPage < maxPages)) {
                console.log(`Moving to page ${currentPage + 1}...`);
                await nextButton.click();
                await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
                await page.waitForSelector('.card-wrapper', { timeout: 15000 });
                currentPage++;
            } else {
                if (maxPages !== null && currentPage >= maxPages) {
                    console.log(`Reached maximum pages limit (${maxPages}). Stopping.`);
                } else {
                    console.log('No more pages found. Scraping complete.');
                }
                break;
            }
        }
        
    } catch (error) {
        console.error('Error during scraping:', error);
    } finally {
        await browser.close();
    }

    // Calculate metrics
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000; // in seconds
    const avgTimePerJob = successfulJobs > 0 ? (totalTime / successfulJobs).toFixed(2) : 0;

    // Save results to JSON file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${category}_${timestamp}.json`;

    fs.writeFileSync(filename, JSON.stringify(allJobs, null, 2));

    // Display comprehensive metrics
    console.log(`\n${'='.repeat(50)}`);
    console.log(`SCRAPING METRICS FOR ${category.toUpperCase()}`);
    console.log(`${'='.repeat(50)}`);
    console.log(`📊 Total jobs found: ${allJobs.length}`);
    console.log(`✅ Successful extractions: ${successfulJobs}`);
    console.log(`❌ Failed extractions: ${failedJobs}`);
    console.log(`📄 Total pages scraped: ${totalPagesScraped}`);
    console.log(`⏱️  Total time: ${totalTime.toFixed(2)} seconds`);
    console.log(`⚡ Average time per job: ${avgTimePerJob} seconds`);
    console.log(`💾 Results saved to: ${filename}`);
    console.log(`${'='.repeat(50)}`);

    return {
        jobs: allJobs,
        metrics: {
            totalJobs: allJobs.length,
            successfulJobs,
            failedJobs,
            totalPagesScraped,
            totalTimeSeconds: totalTime,
            avgTimePerJob: parseFloat(avgTimePerJob),
            filename,
            category
        }
    };
}

// Helper function to display available categories
function showCategories() {
    console.log('\nAvailable categories:');
    Object.keys(CATEGORIES).forEach((key, index) => {
        console.log(`${index + 1}. ${key} - ${CATEGORIES[key]}`);
    });
    console.log('\nUsage examples:');
    console.log('node bursary-scraper.js bursaries');
    console.log('node bursary-scraper.js internships 5  # Scrape only first 5 pages');
    console.log('npm run scrape-category bursaries');
}

// Run the scraper
if (require.main === module) {
    const args = process.argv.slice(2);
    const category = args[0] || 'bursaries';
    const maxPages = args[1] ? parseInt(args[1]) : null;

    if (args.includes('--help') || args.includes('-h')) {
        showCategories();
        process.exit(0);
    }

    if (!CATEGORIES[category]) {
        console.error(`❌ Invalid category: ${category}`);
        showCategories();
        process.exit(1);
    }

    console.log(`Starting scraper for category: ${category}`);
    if (maxPages) {
        console.log(`Limited to first ${maxPages} pages`);
    }

    scrapeJobs(category, maxPages).catch(console.error);
}

module.exports = { scrapeJobs, CATEGORIES, showCategories };
