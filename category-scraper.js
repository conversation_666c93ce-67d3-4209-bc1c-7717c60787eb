const { scrapeJobs, CATEGORIES, showCategories } = require('./bursary-scraper');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, resolve);
    });
}

async function interactiveScraper() {
    console.log('🚀 Interactive Job Scraper for Spane4all.co.za');
    console.log('=' .repeat(50));
    
    // Show available categories
    console.log('\nAvailable categories:');
    const categoryKeys = Object.keys(CATEGORIES);
    categoryKeys.forEach((key, index) => {
        console.log(`${index + 1}. ${key.toUpperCase()}`);
    });
    
    try {
        // Get category choice
        const categoryChoice = await askQuestion('\nEnter category number or name: ');
        
        let selectedCategory;
        if (!isNaN(categoryChoice)) {
            // User entered a number
            const index = parseInt(categoryChoice) - 1;
            if (index >= 0 && index < categoryKeys.length) {
                selectedCategory = categoryKeys[index];
            }
        } else {
            // User entered a category name
            selectedCategory = categoryChoice.toLowerCase();
        }
        
        if (!selectedCategory || !CATEGORIES[selectedCategory]) {
            console.log('❌ Invalid category selection.');
            rl.close();
            return;
        }
        
        console.log(`✅ Selected category: ${selectedCategory.toUpperCase()}`);
        console.log(`📍 URL: ${CATEGORIES[selectedCategory]}`);
        
        // Get max pages limit
        const maxPagesInput = await askQuestion('\nEnter max pages to scrape (press Enter for all pages): ');
        const maxPages = maxPagesInput.trim() === '' ? null : parseInt(maxPagesInput);
        
        if (maxPages !== null && (isNaN(maxPages) || maxPages <= 0)) {
            console.log('❌ Invalid page limit. Please enter a positive number.');
            rl.close();
            return;
        }
        
        // Confirm before starting
        console.log('\n📋 Scraping Configuration:');
        console.log(`   Category: ${selectedCategory.toUpperCase()}`);
        console.log(`   Max Pages: ${maxPages || 'All pages'}`);
        console.log(`   URL: ${CATEGORIES[selectedCategory]}`);
        
        const confirm = await askQuestion('\nStart scraping? (y/n): ');
        
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log('❌ Scraping cancelled.');
            rl.close();
            return;
        }
        
        rl.close();
        
        // Start scraping
        console.log('\n🔄 Starting scraper...');
        const result = await scrapeJobs(selectedCategory, maxPages);
        
        // Display final summary
        console.log('\n🎉 SCRAPING COMPLETED SUCCESSFULLY!');
        console.log('\n📊 Final Summary:');
        console.log(`   Category: ${result.metrics.category.toUpperCase()}`);
        console.log(`   Total Jobs Found: ${result.metrics.totalJobs}`);
        console.log(`   Successful Extractions: ${result.metrics.successfulJobs}`);
        console.log(`   Failed Extractions: ${result.metrics.failedJobs}`);
        console.log(`   Pages Scraped: ${result.metrics.totalPagesScraped}`);
        console.log(`   Total Time: ${result.metrics.totalTimeSeconds.toFixed(2)} seconds`);
        console.log(`   Average Time per Job: ${result.metrics.avgTimePerJob} seconds`);
        console.log(`   Success Rate: ${((result.metrics.successfulJobs / (result.metrics.successfulJobs + result.metrics.failedJobs)) * 100).toFixed(1)}%`);
        console.log(`   File Saved: ${result.metrics.filename}`);
        
        // Show sample jobs
        if (result.jobs.length > 0) {
            console.log('\n📝 Sample Jobs (first 3):');
            result.jobs.slice(0, 3).forEach((job, index) => {
                console.log(`\n   ${index + 1}. ${job.title || 'No Title'}`);
                console.log(`      Organization: ${job.organization || 'N/A'}`);
                console.log(`      Location: ${job.jobLocation || 'N/A'}`);
                console.log(`      Type: ${job.employmentType || 'N/A'}`);
                console.log(`      Closing Date: ${job.closingDate || 'N/A'}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error during scraping:', error);
        rl.close();
    }
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log('🚀 Interactive Job Scraper for Spane4all.co.za');
        console.log('\nUsage:');
        console.log('  node category-scraper.js                    # Interactive mode');
        console.log('  node category-scraper.js bursaries          # Direct category');
        console.log('  node category-scraper.js internships 5      # Category with page limit');
        console.log('  node category-scraper.js --help             # Show this help');
        console.log('\nAvailable categories:');
        Object.keys(CATEGORIES).forEach(key => {
            console.log(`  - ${key}`);
        });
        process.exit(0);
    }
    
    if (args.length === 0) {
        // Interactive mode
        interactiveScraper();
    } else {
        // Direct command line mode
        const category = args[0];
        const maxPages = args[1] ? parseInt(args[1]) : null;
        
        if (!CATEGORIES[category]) {
            console.error(`❌ Invalid category: ${category}`);
            showCategories();
            process.exit(1);
        }
        
        console.log(`🚀 Starting scraper for ${category.toUpperCase()}`);
        if (maxPages) {
            console.log(`📄 Limited to first ${maxPages} pages`);
        }
        
        scrapeJobs(category, maxPages)
            .then(result => {
                console.log('\n🎉 Scraping completed successfully!');
                console.log(`📊 Total jobs: ${result.metrics.totalJobs}`);
                console.log(`📄 Pages scraped: ${result.metrics.totalPagesScraped}`);
                console.log(`💾 Saved to: ${result.metrics.filename}`);
            })
            .catch(console.error);
    }
}
