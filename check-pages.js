const { chromium } = require('playwright');

const CATEGORIES = {
    bursaries: 'https://www.spane4all.co.za/bursaries',
    internships: 'https://spane4all.co.za/internships',
    jobs: 'https://spane4all.co.za/jobs',
    scholarships: 'https://www.spane4all.co.za/scholarships',
    general_jobs: 'https://www.spane4all.co.za/general_jobs',
    government_intern: 'https://www.spane4all.co.za/government_intern',
    government_job: 'https://www.spane4all.co.za/government_job'
};

async function checkPagesForCategory(category) {
    if (!CATEGORIES[category]) {
        console.error(`Invalid category: ${category}`);
        return;
    }
    
    const browser = await chromium.launch({ headless: false });
    const page = await browser.newPage();
    
    try {
        console.log(`\n🔍 Checking ${category.toUpperCase()}`);
        console.log(`📍 URL: ${CATEGORIES[category]}`);
        
        await page.goto(CATEGORIES[category], { waitUntil: 'domcontentloaded', timeout: 60000 });
        
        // Wait for content to load
        await page.waitForSelector('.card-wrapper', { timeout: 30000 });
        await page.waitForTimeout(3000);
        
        // Check for pagination info
        const pageInfo = await page.evaluate(() => {
            const text = document.body.textContent;
            const pageMatch = text.match(/Page\s+(\d+)\s+of\s+(\d+)/i);
            
            // Count apply buttons
            const applyButtons = document.querySelectorAll('a.btn.btn-primary.mt-2');
            const applyButtonsWithText = Array.from(applyButtons).filter(btn => btn.textContent.includes('Apply'));

            // Look for pagination controls
            const allLinks = document.querySelectorAll('a');
            const nextButton = Array.from(allLinks).find(link => link.textContent.includes('Next'));
            const prevButton = Array.from(allLinks).find(link => link.textContent.includes('Previous'));
            
            return {
                currentPage: pageMatch ? parseInt(pageMatch[1]) : 1,
                totalPages: pageMatch ? parseInt(pageMatch[2]) : null,
                applyButtonsCount: applyButtonsWithText.length,
                hasNextButton: !!nextButton,
                hasPrevButton: !!prevButton,
                pageText: pageMatch ? pageMatch[0] : 'No pagination info found'
            };
        });
        
        console.log(`📊 Results for ${category}:`);
        console.log(`   Current Page: ${pageInfo.currentPage}`);
        console.log(`   Total Pages: ${pageInfo.totalPages || 'Unknown'}`);
        console.log(`   Apply Buttons Found: ${pageInfo.applyButtonsCount}`);
        console.log(`   Has Next Button: ${pageInfo.hasNextButton ? '✅' : '❌'}`);
        console.log(`   Has Previous Button: ${pageInfo.hasPrevButton ? '✅' : '❌'}`);
        console.log(`   Pagination Text: "${pageInfo.pageText}"`);
        
        if (pageInfo.totalPages) {
            console.log(`\n💡 Recommendations for ${category}:`);
            if (pageInfo.totalPages > 50) {
                console.log(`   ⚠️  Large dataset (${pageInfo.totalPages} pages) - consider limiting to 10-20 pages`);
                console.log(`   📝 Command: node bursary-scraper.js ${category} 20`);
            } else if (pageInfo.totalPages > 10) {
                console.log(`   📊 Medium dataset (${pageInfo.totalPages} pages) - manageable size`);
                console.log(`   📝 Command: node bursary-scraper.js ${category}`);
            } else {
                console.log(`   ✅ Small dataset (${pageInfo.totalPages} pages) - quick to scrape`);
                console.log(`   📝 Command: node bursary-scraper.js ${category}`);
            }
        }
        
        return pageInfo;
        
    } catch (error) {
        console.error(`❌ Error checking ${category}:`, error.message);
        return null;
    } finally {
        await browser.close();
    }
}

async function checkAllCategories() {
    console.log('🚀 Checking pagination info for all categories...');
    console.log('=' .repeat(60));
    
    const results = {};
    
    for (const category of Object.keys(CATEGORIES)) {
        const result = await checkPagesForCategory(category);
        results[category] = result;
        
        // Wait between requests to be respectful
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Summary
    console.log('\n📋 SUMMARY');
    console.log('=' .repeat(60));
    
    Object.entries(results).forEach(([category, info]) => {
        if (info) {
            console.log(`${category.padEnd(15)} | ${info.totalPages || 'Unknown'} pages | ${info.applyButtonsCount} jobs/page`);
        } else {
            console.log(`${category.padEnd(15)} | Error checking`);
        }
    });
    
    console.log('\n💡 Quick Commands:');
    Object.entries(results).forEach(([category, info]) => {
        if (info && info.totalPages) {
            const recommendedLimit = info.totalPages > 50 ? 20 : info.totalPages > 10 ? Math.min(10, info.totalPages) : info.totalPages;
            console.log(`npm run scrape-${category.replace('_', '-')}     # ${info.totalPages} pages total`);
            if (info.totalPages > 10) {
                console.log(`node bursary-scraper.js ${category} ${recommendedLimit}     # Limited scraping`);
            }
        }
    });
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        checkAllCategories();
    } else {
        const category = args[0];
        checkPagesForCategory(category);
    }
}
