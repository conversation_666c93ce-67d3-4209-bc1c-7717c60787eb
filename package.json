{"name": "playwright", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "scrape": "node category-scraper.js", "scrape-bursaries": "node bursary-scraper.js bursaries", "scrape-internships": "node bursary-scraper.js internships", "scrape-jobs": "node bursary-scraper.js jobs", "scrape-scholarships": "node bursary-scraper.js scholarships", "scrape-general": "node bursary-scraper.js general_jobs", "scrape-gov-intern": "node bursary-scraper.js government_intern", "scrape-gov-jobs": "node bursary-scraper.js government_job", "test-scraper": "node test-scraper.js", "quick-test": "node quick-test.js", "check-pages": "node check-pages.js", "scrape-all": "node scrape-all-categories.js 10", "scrape-all-5": "node scrape-all-categories.js 5", "help": "node bursary-scraper.js --help"}, "keywords": [], "author": "", "license": "ISC", "description": "Playwright scraper for bursary listings", "dependencies": {"playwright": "^1.53.2"}}