const { scrapeJobs } = require('./bursary-scraper');

async function testScraper() {
    console.log('Starting job scraper test...');
    try {
        // Test with bursaries, limited to 2 pages
        const result = await scrapeJobs('bursaries', 2);
        console.log(`Test completed successfully! Scraped ${result.jobs.length} jobs.`);

        // Display metrics
        console.log('\nTest Metrics:');
        console.log(`Category: ${result.metrics.category}`);
        console.log(`Total Jobs: ${result.metrics.totalJobs}`);
        console.log(`Successful: ${result.metrics.successfulJobs}`);
        console.log(`Failed: ${result.metrics.failedJobs}`);
        console.log(`Pages Scraped: ${result.metrics.totalPagesScraped}`);
        console.log(`Total Time: ${result.metrics.totalTimeSeconds}s`);

        // Display first few results as sample
        if (result.jobs.length > 0) {
            console.log('\nSample results:');
            result.jobs.slice(0, 3).forEach((job, index) => {
                console.log(`\n--- Job ${index + 1} ---`);
                console.log(`Organization: ${job.organization || 'N/A'}`);
                console.log(`Employment Type: ${job.employmentType || 'N/A'}`);
                console.log(`Location: ${job.jobLocation || 'N/A'}`);
                console.log(`Salary: ${job.baseSalary || 'N/A'}`);
                console.log(`Closing Date: ${job.closingDate || 'N/A'}`);
                console.log(`Apply URL: ${job.applyUrl || 'N/A'}`);
                console.log(`Source URL: ${job.sourceUrl || 'N/A'}`);
            });
        }
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testScraper();
